package logic_activity

import (
	"activitysrv/internal/model"
	"context"
	"testing"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
)

func TestPrepareRewardsAndUpdateUserData(t *testing.T) {
	// 准备测试数据
	userData := model.NewUserActivityData()
	availableStages := map[int32][]cmodel.StageRewardsRewards{
		1: {
			{ItemId: 1001, Count: 10},
			{ItemId: 1002, Count: 5},
		},
		2: {
			{ItemId: 2001, Count: 20},
		},
	}

	// 执行函数
	rewardList, claimedStages := prepareRewardsAndUpdateUserData(availableStages, userData)

	// 验证结果
	if len(rewardList) != 3 {
		t.Errorf("Expected 3 rewards, got %d", len(rewardList))
	}

	if len(claimedStages) != 2 {
		t.<PERSON>rrorf("Expected 2 claimed stages, got %d", len(claimedStages))
	}

	// 验证奖励内容
	expectedRewards := map[int64]int64{
		1001: 10,
		1002: 5,
		2001: 20,
	}

	for _, reward := range rewardList {
		if expectedCount, exists := expectedRewards[reward.ItemId]; !exists {
			t.Errorf("Unexpected reward item: %d", reward.ItemId)
		} else if reward.ItemCount != expectedCount {
			t.Errorf("Expected count %d for item %d, got %d", expectedCount, reward.ItemId, reward.ItemCount)
		}
	}

	// 验证用户数据更新
	for _, stageId := range claimedStages {
		if !userData.IsStageClaimedStage(stageId) {
			t.Errorf("Stage %d should be marked as claimed", stageId)
		}
	}
}

func TestConsolidateRewards(t *testing.T) {
	// 准备测试数据
	rewardList := []*commonPB.ItemBase{
		{ItemId: 1001, ItemCount: 10},
		{ItemId: 1002, ItemCount: 5},
		{ItemId: 1001, ItemCount: 15}, // 重复物品
		{ItemId: 1003, ItemCount: 20},
		{ItemId: 1002, ItemCount: 10}, // 重复物品
	}

	// 执行函数
	consolidated := consolidateRewards(rewardList)

	// 验证结果
	if len(consolidated) != 3 {
		t.Errorf("Expected 3 consolidated items, got %d", len(consolidated))
	}

	// 验证合并结果
	expectedCounts := map[int64]int64{
		1001: 25, // 10 + 15
		1002: 15, // 5 + 10
		1003: 20,
	}

	for _, item := range consolidated {
		if expectedCount, exists := expectedCounts[item.ItemId]; !exists {
			t.Errorf("Unexpected item: %d", item.ItemId)
		} else if item.ItemCount != expectedCount {
			t.Errorf("Expected count %d for item %d, got %d", expectedCount, item.ItemId, item.ItemCount)
		}
	}
}

func TestValidateActivityCycle_InvalidCycle(t *testing.T) {
	// 这是一个示例测试，实际测试需要mock依赖
	ctx := context.Background()
	
	// 注意：这个测试需要mock dao_activity.GetCurrentCycle
	// 在实际项目中，应该使用依赖注入和mock来进行单元测试
	
	t.Skip("Skipping integration test - requires mocked dependencies")
	
	_, err := validateActivityCycle(ctx, 1, 999)
	if err == nil {
		t.Error("Expected error for invalid cycle, got nil")
	}
}

// 基准测试
func BenchmarkPrepareRewardsAndUpdateUserData(b *testing.B) {
	userData := model.NewUserActivityData()
	availableStages := map[int32][]cmodel.StageRewardsRewards{
		1: {{ItemId: 1001, Count: 10}},
		2: {{ItemId: 1002, Count: 20}},
		3: {{ItemId: 1003, Count: 30}},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		// 重置用户数据
		userData = model.NewUserActivityData()
		prepareRewardsAndUpdateUserData(availableStages, userData)
	}
}

func BenchmarkConsolidateRewards(b *testing.B) {
	rewardList := []*commonPB.ItemBase{
		{ItemId: 1001, ItemCount: 10},
		{ItemId: 1002, ItemCount: 5},
		{ItemId: 1001, ItemCount: 15},
		{ItemId: 1003, ItemCount: 20},
		{ItemId: 1002, ItemCount: 10},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		consolidateRewards(rewardList)
	}
}
