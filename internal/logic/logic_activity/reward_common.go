package logic_activity

import (
	"activitysrv/config"
	"activitysrv/internal/dao/dao_activity"
	"activitysrv/internal/model"
	"activitysrv/internal/repo/rpc_msg"
	"context"
	"fmt"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/item_kit"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	"git.keepfancy.xyz/back-end/frameworks/kit/dlm"
	"git.keepfancy.xyz/back-end/frameworks/lib/timex"
)

// RewardDeliveryMethod 奖励发放方式
type RewardDeliveryMethod int

const (
	// RewardDeliveryDirect 直接发放（手动领奖）
	RewardDeliveryDirect RewardDeliveryMethod = iota
	// RewardDeliveryMail 邮件发放（自动领奖）
	RewardDeliveryMail
)

// RewardClaimResult 奖励领取结果
type RewardClaimResult struct {
	ClaimedStages []int32
	RewardList    []*commonPB.ItemBase
}

// validateActivityCycle 校验活动时间和周期有效性
func validateActivityCycle(ctx context.Context, activityId int64, cycleId int32) (*model.ActivityCycle, error) {
	currentCycle, err := dao_activity.GetCurrentCycle(ctx, activityId)
	if err != nil {
		return nil, fmt.Errorf("failed to get current cycle: %w", err)
	}
	if currentCycle == nil {
		return nil, fmt.Errorf("no active activity cycle currently")
	}

	// 检查周期是否有效
	if cycleId != currentCycle.CycleId && cycleId != currentCycle.CycleId-1 {
		return nil, fmt.Errorf("rewards for cycle %d have expired, only current cycle and previous cycle rewards are supported", cycleId)
	}

	return currentCycle, nil
}

// processRewardClaim 处理奖励领取的通用逻辑
func processRewardClaim(ctx context.Context, playerId uint64, activityId int64, cycleId int32, activityCfg *cmodel.Activity, currentCycle *model.ActivityCycle, deliveryMethod RewardDeliveryMethod) ([]int32, error) {
	entry := logx.NewLogEntry(ctx)

	// 加锁
	unlock := dlm.LockKey(config.UserActivityLockKey(activityId, playerId))
	defer unlock()

	// 获取用户数据
	userData, err := dao_activity.GetUserData(ctx, activityId, playerId, cycleId)
	if err != nil {
		return nil, fmt.Errorf("failed to get user data: %w", err)
	}

	// 筛选玩家当前可以领取的阶段
	availableStages, err := getAvailableStagesForClaim(ctx, activityCfg, userData)
	if err != nil {
		return nil, fmt.Errorf("failed to get available stages for claim: %w", err)
	}

	if len(availableStages) == 0 {
		return nil, fmt.Errorf("no reward stages available for claim currently")
	}

	// 准备奖励数据和更新用户状态
	rewardList, claimedStages := prepareRewardsAndUpdateUserData(availableStages, userData)

	// 先更新玩家领取阶段，再实际领取
	err = dao_activity.SaveUserData(ctx, playerId, activityCfg, currentCycle, userData)
	if err != nil {
		return nil, fmt.Errorf("failed to update user data: %w", err)
	}

	// 根据发放方式选择不同的奖励发放逻辑
	err = deliverRewards(ctx, playerId, rewardList, deliveryMethod)
	if err != nil {
		entry.Errorf("failed to deliver rewards: %v", err)
		return nil, fmt.Errorf("failed to deliver rewards: %w", err)
	}

	// 成功领取奖励后，从周期记录中移除玩家
	_ = dao_activity.RemovePlayerFromCycle(ctx, activityId, cycleId, playerId)

	return claimedStages, nil
}

// deliverRewards 根据发放方式发放奖励
func deliverRewards(ctx context.Context, playerId uint64, rewardList []*commonPB.ItemBase, deliveryMethod RewardDeliveryMethod) error {
	entry := logx.NewLogEntry(ctx)

	switch deliveryMethod {
	case RewardDeliveryDirect:
		// 直接发放奖励（手动领奖）
		rewardInfo, err := item_kit.SendReward(ctx, playerId, rewardList, commonPB.ITEM_SOURCE_TYPE_IST_ACTIVITY_REWARD, false)
		if err != nil {
			entry.Errorf("failed to send rewards directly: %v rewardInfo=%v", err, rewardInfo)
			return fmt.Errorf("failed to send rewards directly: %w", err)
		}
		return nil

	case RewardDeliveryMail:
		// 通过邮件发放奖励（自动领奖）
		mailRewardList := consolidateRewards(rewardList)
		mailInfo := SendActivityAwardMail(ctx, playerId, mailRewardList)
		err := rpc_msg.SendMail(ctx, playerId, mailInfo)
		if err != nil {
			entry.Errorf("failed to send mail: %v", err)
			return fmt.Errorf("failed to send mail: %w", err)
		}
		return nil

	default:
		return fmt.Errorf("unsupported reward delivery method: %d", deliveryMethod)
	}
}

// consolidateRewards 合并相同物品的奖励
func consolidateRewards(rewardList []*commonPB.ItemBase) []*commonPB.ItemBase {
	itemMap := make(map[int64]*commonPB.ItemBase)
	for _, one := range rewardList {
		if existing, ok := itemMap[one.ItemId]; ok {
			existing.ItemCount += one.ItemCount
		} else {
			itemMap[one.ItemId] = &commonPB.ItemBase{
				ItemId:    one.ItemId,
				ItemCount: one.ItemCount,
			}
		}
	}

	mailRewardList := make([]*commonPB.ItemBase, 0, len(itemMap))
	for _, item := range itemMap {
		mailRewardList = append(mailRewardList, item)
	}
	return mailRewardList
}

// SendActivityAwardMail 发送邮件
func SendActivityAwardMail(ctx context.Context, playerId uint64, itemList []*commonPB.ItemBase) *commonPB.MailAssembly {
	if len(itemList) == 0 {
		return nil
	}

	extend := make(map[int32]int64)

	mail := &commonPB.MailAssembly{
		TemplateId:  config.ACTIVITY_REWARD_EMAIL_TEMPLATE,
		Extend:      extend,
		MailType:    commonPB.MAIL_TYPE_MT_ORDINARY,
		CreateTime:  timex.Now().Unix(),
		ExpiresTime: timex.Now().Unix() + 7*86400,
		Rewards: &commonPB.ItemBaseList{
			ItemList: itemList,
		},
	}

	return mail
}

// prepareRewardsAndUpdateUserData 准备奖励数据并更新用户状态
func prepareRewardsAndUpdateUserData(availableStages map[int32][]cmodel.StageRewardsRewards, userData *model.UserActivityData) ([]*commonPB.ItemBase, []int32) {
	now := timex.Now().Unix()
	var rewardList []*commonPB.ItemBase
	stages := make([]int32, 0, len(availableStages))

	for stageId, rewards := range availableStages {
		stages = append(stages, stageId)
		userData.ClaimedStages[stageId] = now
		for _, reward := range rewards {
			rewardList = append(rewardList, &commonPB.ItemBase{
				ItemId:    reward.ItemId,
				ItemCount: reward.Count,
			})
		}
	}

	return rewardList, stages
}

// getAvailableStagesForClaim 获取玩家当前可以领取的所有阶段
func getAvailableStagesForClaim(ctx context.Context, activityCfg *cmodel.Activity, userData *model.UserActivityData) (map[int32][]cmodel.StageRewardsRewards, error) {

	allStageConfigs := cmodel.GetAllStages(consul_config.WithGrpcCtx(ctx))
	if allStageConfigs == nil {
		return nil, fmt.Errorf("failed to get stage configuration")
	}

	res := make(map[int32][]cmodel.StageRewardsRewards)
	curProgress := userData.Metrics[activityCfg.Target]
	for _, stageCfg := range allStageConfigs {
		// 检查是否是当前活动的阶段
		if stageCfg.ActivityId != activityCfg.Id {
			continue
		}

		// 检查是否已经领取过
		if userData.IsStageClaimedStage(stageCfg.StageId) {
			continue
		}

		// 检查是否满足领取条件
		if curProgress >= stageCfg.Value {
			stageRewards := cmodel.GetStageRewards(int64(stageCfg.StageId), consul_config.WithGrpcCtx(ctx))
			if stageRewards == nil {
				return nil, fmt.Errorf("failed to get stage reward configuration: %d", stageCfg.StageId)
			}
			res[stageCfg.StageId] = stageRewards.Rewards

		}
	}

	return res, nil
}
