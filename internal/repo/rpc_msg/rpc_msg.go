package rpc_msg

import (
	"context"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	msgrpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/msgrpc"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/rpc_biz/crpc_msg"
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"

	"github.com/spf13/viper"
)

// SendMail 发送邮件
func SendMail(ctx context.Context, playerId uint64, mail *commonPB.MailAssembly) error {
	if mail == nil {
		return protox.CodeError(commonPB.ErrCode_ERR_BAD_PARAM, "mail is nil")
	}

	cli := crpc_msg.GetMsgRpcInstance().GetMsgRpcClient()
	if cli == nil {
		return protox.CodeError(commonPB.ErrCode_ERR_OPERATION, "rpc client fail")
	}

	req := &msgrpc.SendMailReq{
		Sender:    viper.GetString(dict.ConfigRpcServerName),
		PlayerIds: []uint64{playerId},
		ProductId: commonPB.PRODUCT_ID(interceptor.GetRPCOptions(ctx).ProductId),
		Channel:   commonPB.CHANNEL_TYPE(interceptor.GetRPCOptions(ctx).ChannelType),
		Assembly:  mail,
	}

	_, errRet := cli.SendMail(ctx, req)
	if errRet != nil {
		return errRet
	}

	return nil
}
