package logic_activity

import (
	"activitysrv/config"
	"activitysrv/internal/dao/dao_activity"
	"activitysrv/internal/model"
	"context"
	"fmt"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	activityPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/activity"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/item_kit"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/operate"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	"git.keepfancy.xyz/back-end/frameworks/kit/dlm"
	"git.keepfancy.xyz/back-end/frameworks/lib/timex"
	"github.com/sirupsen/logrus"
	"time"
)

// ActivityLogic 活动业务逻辑
type ActivityLogic struct {
}

func NewActivityLogic() *ActivityLogic {
	return &ActivityLogic{}
}

// HandleEvent 处理事件 - 实现ActivityHandler接口
func (logic *ActivityLogic) HandleEvent(ctx context.Context, playerId uint64, event *commonPB.EventCommon) error {
	entry := logx.NewLogEntry(ctx)

	// 1. 获取所有活动配置
	allActivities := cmodel.GetAllActivity(consul_config.WithGrpcCtx(ctx))
	if allActivities == nil {
		entry.Debugf("activity configuration not found")
		return nil
	}

	// 2. 筛选当前开启且支持该事件类型的活动
	activeActivities := filterActiveActivitiesForEvent(allActivities, event)
	if len(activeActivities) == 0 {
		entry.Debugf("no active activities need to handle this event: eventType=%v", event.EventType)
		return nil
	}

	for _, activityCfg := range activeActivities {
		err := processActivityEvent(ctx, playerId, event, activityCfg)
		if err != nil {
			entry.Errorf("failed to process activity event: activityId=%d, err=%v", activityCfg.Id, err)
		}
	}

	return nil
}

// GetProgress 获取活动进度 - 实现ActivityHandler接口
func (logic *ActivityLogic) GetProgress(ctx context.Context, playerId uint64, req *activityPB.GetActivityProgressReq) ([]*activityPB.ActivityProgress, error) {
	entry := logx.NewLogEntry(ctx)

	activityId := int64(req.GetActivityId())
	// 获取活动配置
	activityCfg := cmodel.GetActivity(activityId, consul_config.WithGrpcCtx(ctx))
	if activityCfg == nil {
		return nil, fmt.Errorf("activity configuration not found")
	}

	// 获取当前周期
	currentCycle, err := CheckAndCreateCycleIfNeeded(ctx, activityCfg)
	if err != nil {
		return nil, fmt.Errorf("failed to get current cycle: %w", err)
	}

	// 获取当前周期的用户数据
	currentUserData, err := dao_activity.GetUserData(ctx, activityId, playerId, currentCycle.CycleId)
	if err != nil {
		return nil, fmt.Errorf("failed to get current cycle user data: %w", err)
	}

	var res []*activityPB.ActivityProgress
	// 构建进度数据
	progress := &activityPB.ActivityProgress{
		ActivityId:     req.GetActivityId(),
		CurrentCycleId: currentCycle.CycleId,
		CycleEndTime:   currentCycle.EndTime,
		Metrics:        currentUserData.Metrics,
		ClaimedRecords: currentUserData.GetClaimedStagesList(),
	}

	res = append(res, progress)

	// 获取上个周期数据（如果存在）
	if currentCycle.CycleId > 1 {
		previousCycleId := currentCycle.CycleId - 1
		previousUserData, err := dao_activity.GetUserData(ctx, activityId, playerId, previousCycleId)
		if err != nil {
			entry.Warnf("failed to get previous cycle user data: %v", err)
		} else {
			previousCycle := &activityPB.ActivityProgress{
				ActivityId:     req.GetActivityId(),
				CurrentCycleId: previousCycleId,
				Metrics:        previousUserData.Metrics,
				ClaimedRecords: previousUserData.GetClaimedStagesList(),
			}
			res = append(res, previousCycle)
		}
	}

	entry.Debugf("successfully retrieved activity progress: activityId=%d, playerId=%d, currentCycleId=%d",
		req.GetActivityId(), playerId, currentCycle.CycleId)

	return res, nil
}

// ClaimReward 领取奖励
func (logic *ActivityLogic) ClaimReward(ctx context.Context, playerId uint64, req *activityPB.ClaimActivityRewardReq) (*activityPB.ClaimActivityRewardRsp, error) {
	entry := logx.NewLogEntry(ctx)

	activityId := int64(req.GetActivityId())

	// 获取活动配置
	activityCfg := cmodel.GetActivity(activityId, consul_config.WithGrpcCtx(ctx))
	if activityCfg == nil {
		return nil, fmt.Errorf("explosive protection activity configuration not found")
	}

	// 校验活动时间
	currentCycle, err := dao_activity.GetCurrentCycle(ctx, activityId)
	if err != nil {
		return nil, fmt.Errorf("failed to get current cycle: %w", err)
	}
	if currentCycle == nil {
		return nil, fmt.Errorf("no active activity cycle currently")
	}

	// 检查周期是否有效
	if req.GetCycleId() != currentCycle.CycleId && req.GetCycleId() != currentCycle.CycleId-1 {
		return nil, fmt.Errorf("rewards for cycle %d have expired, only current cycle and previous cycle rewards are supported", req.GetCycleId())
	}

	// 加锁
	unlock := dlm.LockKey(config.UserActivityLockKey(activityId, playerId))
	defer unlock()

	// 获取用户数据
	userData, err := dao_activity.GetUserData(ctx, activityId, playerId, req.GetCycleId())
	if err != nil {
		return nil, fmt.Errorf("failed to get user data: %w", err)
	}

	// 筛选玩家当前可以领取的阶段
	availableStages, err := getAvailableStagesForClaim(ctx, activityCfg, userData)
	if err != nil {
		return nil, fmt.Errorf("failed to get available stages for claim: %w", err)
	}

	if len(availableStages) == 0 {
		return nil, fmt.Errorf("no reward stages available for claim currently")
	}

	// 序列化奖励和阶段
	now := timex.Now().Unix()
	var rewardList []*commonPB.ItemBase
	stages := make([]int32, 0, len(availableStages))
	for stageId, rewards := range availableStages {
		stages = append(stages, stageId)
		userData.ClaimedStages[stageId] = now
		for _, reward := range rewards {
			rewardList = append(rewardList, &commonPB.ItemBase{
				ItemId:    reward.ItemId,
				ItemCount: reward.Count,
			})
		}
	}

	// 先更新玩家领取阶段，再实际领取
	err = dao_activity.SaveUserData(ctx, playerId, activityCfg, currentCycle, userData)
	if err != nil {
		return nil, fmt.Errorf("failed to update user data: %w", err)
	}

	rewardInfo, err := item_kit.SendReward(ctx, playerId, rewardList, commonPB.ITEM_SOURCE_TYPE_IST_ACTIVITY_REWARD, false)
	if err != nil {
		entry.Errorf("failed to send rewards: %v rewardInfo=%v", err, rewardInfo)
		return nil, fmt.Errorf("failed to send rewards: %w", err)
	}

	// 成功领取奖励后，从周期记录中移除玩家
	_ = dao_activity.RemovePlayerFromCycle(ctx, activityId, req.GetCycleId(), playerId)

	res := &activityPB.ClaimActivityRewardRsp{
		ActivityId: req.GetActivityId(),
		StageId:    stages,
	}

	return res, nil
}

// getAvailableStagesForClaim 获取玩家当前可以领取的所有阶段
func getAvailableStagesForClaim(ctx context.Context, activityCfg *cmodel.Activity, userData *model.UserActivityData) (map[int32][]cmodel.StageRewardsRewards, error) {

	allStageConfigs := cmodel.GetAllStages(consul_config.WithGrpcCtx(ctx))
	if allStageConfigs == nil {
		return nil, fmt.Errorf("failed to get stage configuration")
	}

	res := make(map[int32][]cmodel.StageRewardsRewards)
	curProgress := userData.Metrics[activityCfg.Target]
	for _, stageCfg := range allStageConfigs {
		// 检查是否是当前活动的阶段
		if stageCfg.ActivityId != activityCfg.Id {
			continue
		}

		// 检查是否已经领取过
		if userData.IsStageClaimedStage(stageCfg.StageId) {
			continue
		}

		// 检查是否满足领取条件
		if curProgress >= stageCfg.Value {
			stageRewards := cmodel.GetStageRewards(int64(stageCfg.StageId), consul_config.WithGrpcCtx(ctx))
			if stageRewards == nil {
				return nil, fmt.Errorf("failed to get stage reward configuration: %d", stageCfg.StageId)
			}
			res[stageCfg.StageId] = stageRewards.Rewards

		}
	}

	return res, nil
}

// isActivityActive 检查活动是否活跃
func isActivityActive(cfg *cmodel.Activity) bool {
	// 检查活动时间
	now := time.Now().Unix()
	if now < cfg.OpenAt || (cfg.CloseAt > 0 && now > cfg.CloseAt) {
		return false
	}

	return true
}

// hasActivityMetrics 检查事件是否包含活动相关指标
func hasActivityMetrics(event *commonPB.EventCommon, cfg *cmodel.Activity) bool {
	if _, exists := event.IntData[cfg.Target]; exists {
		return true
	}
	return false
}

// filterActiveActivitiesForEvent 筛选当前开启且支持该事件类型的活动
func filterActiveActivitiesForEvent(allActivities map[int64]*cmodel.Activity, event *commonPB.EventCommon) []*cmodel.Activity {
	var activeActivities []*cmodel.Activity

	for _, activityCfg := range allActivities {
		// 检查活动是否活跃
		if !isActivityActive(activityCfg) {
			continue
		}

		// 检查活动指标是否包含在事件中
		if !hasActivityMetrics(event, activityCfg) {
			continue
		}

		activeActivities = append(activeActivities, activityCfg)
	}

	return activeActivities
}

// processActivityEvent 处理单个活动的事件
func processActivityEvent(ctx context.Context, playerId uint64, event *commonPB.EventCommon, activityCfg *cmodel.Activity) error {
	// 1. 周期管理：检查当前周期是否已结束，如果结束则自动创建新周期
	currentCycle, err := CheckAndCreateCycleIfNeeded(ctx, activityCfg)
	if err != nil {
		return fmt.Errorf("failed to check or create activity cycle: activityId=%d, %w", activityCfg.Id, err)
	}

	// 2. 加锁
	unlock := dlm.LockKey(config.UserActivityLockKey(activityCfg.Id, playerId))
	defer unlock()

	// 3. 获取用户数据
	currentUserData, err := dao_activity.GetUserData(ctx, activityCfg.Id, playerId, currentCycle.CycleId)
	if err != nil {
		return fmt.Errorf("failed to get user data: %w", err)
	}

	// 4. 更新用户指标
	curNum := currentUserData.Metrics[activityCfg.Target]
	oprNum := event.GetIntData()[activityCfg.Target]
	newTarget := operate.SumAddVal(commonPB.SUM_ADD(activityCfg.Update), curNum, oprNum)
	currentUserData.Metrics[activityCfg.Target] = newTarget

	// 5. 保存更新后的用户数据
	currentUserData.MetricsType = activityCfg.Update
	err = dao_activity.SaveUserData(ctx, playerId, activityCfg, currentCycle, currentUserData)
	if err != nil {
		return err
	}

	// 6. 添加玩家到周期记录（成功处理事件后）
	_ = dao_activity.AddPlayerToCycle(ctx, activityCfg.Id, currentCycle.CycleId, playerId)

	return nil
}

// CheckAndCreateCycleIfNeeded 检查并在需要时创建新周期 - 业务逻辑函数
func CheckAndCreateCycleIfNeeded(ctx context.Context, activityCfg *cmodel.Activity) (*model.ActivityCycle, error) {
	currentCycle, err := dao_activity.GetCurrentCycle(ctx, activityCfg.Id)

	if err != nil {
		return nil, err
	}

	// 如果没有当前周期或当前周期已过期，创建新周期
	if currentCycle == nil || currentCycle.IsExpired() {
		return CreateNewCycle(ctx, activityCfg)
	}

	return currentCycle, nil
}

// CreateNewCycle 创建新的活动周期 - 业务逻辑函数
func CreateNewCycle(ctx context.Context, activityCfg *cmodel.Activity) (*model.ActivityCycle, error) {
	now := time.Now().Unix()

	// 1. 验证活动时间配置
	if now < activityCfg.OpenAt {
		return nil, fmt.Errorf("activity has not started yet: activityId=%d, openAt=%d, now=%d",
			activityCfg.Id, activityCfg.OpenAt, now)
	}
	if activityCfg.CloseAt > 0 && now > activityCfg.CloseAt {
		return nil, fmt.Errorf("activity has ended: activityId=%d, closeAt=%d, now=%d",
			activityCfg.Id, activityCfg.CloseAt, now)
	}

	// 2. 计算周期ID和时间
	newCycle, err := calculateCycleIdAndTime(activityCfg, now)
	if err != nil {
		return nil, err
	}

	// 3. 保存到Redis
	if err = dao_activity.SaveCurrentCycle(ctx, activityCfg.Id, newCycle); err != nil {
		return nil, fmt.Errorf("failed to save current cycle: %w", err)
	}

	logrus.Debugf("successfully created new activity cycle: activityId=%d, cycleId=%d, startTime=%d, endTime=%d",
		activityCfg.Id, newCycle.CycleId, newCycle.StartTime, newCycle.EndTime)

	return newCycle, nil
}

// calculateCycleIdAndTime 计算周期ID和时间 - 业务逻辑函数
func calculateCycleIdAndTime(activityCfg *cmodel.Activity, now int64) (newCycle *model.ActivityCycle, err error) {
	var cycleId int32
	var startTime, endTime int64

	if !activityCfg.IsLoop {
		// 非循环活动：周期固定为1，时间就是活动时间
		cycleId = 1
		startTime = activityCfg.OpenAt
		if activityCfg.CloseAt > 0 {
			endTime = activityCfg.CloseAt
		} else {
			cycleDays := activityCfg.CycleDays
			if cycleDays <= 0 {
				cycleDays = 7 // 默认7天
			}
			endTime = startTime + int64(cycleDays)*24*3600
		}
		logrus.Debugf("non-loop activity: activityId=%d, cycleId=%d, startTime=%d, endTime=%d",
			activityCfg.Id, cycleId, startTime, endTime)
		return model.NewActivityCycle(cycleId, activityCfg.CycleDays, startTime, endTime), nil
	}

	// 循环活动：根据CycleDays计算当前周期
	if activityCfg.CycleDays <= 0 {
		return nil, fmt.Errorf("invalid cycle days configuration for loop activity: activityId=%d, cycleDays=%d",
			activityCfg.Id, activityCfg.CycleDays)
	}

	cycleSeconds := int64(activityCfg.CycleDays) * 24 * 3600
	elapsedSeconds := now - activityCfg.OpenAt
	cycleId = int32(elapsedSeconds/cycleSeconds) + 1
	if cycleId <= 0 {
		cycleId = 1
	}

	// 计算周期时间
	startTime = activityCfg.OpenAt + int64(cycleId-1)*cycleSeconds
	endTime = startTime + cycleSeconds

	// 确保不超出活动结束时间
	if activityCfg.CloseAt > 0 && endTime > activityCfg.CloseAt {
		endTime = activityCfg.CloseAt
		// 如果当前周期会超出活动结束时间，调整为最大周期
		maxCycleId := int32((activityCfg.CloseAt-activityCfg.OpenAt)/cycleSeconds) + 1
		if cycleId > maxCycleId {
			cycleId = maxCycleId
		}
	}

	// 验证时间合理性
	if startTime >= endTime {
		return nil, fmt.Errorf("calculated cycle time is invalid: activityId=%d, cycleId=%d, startTime=%d, endTime=%d",
			activityCfg.Id, cycleId, startTime, endTime)
	}

	logrus.Debugf("loop activity: activityId=%d, cycleId=%d, cycleDays=%d, startTime=%d, endTime=%d",
		activityCfg.Id, cycleId, activityCfg.CycleDays, startTime, endTime)

	return model.NewActivityCycle(cycleId, activityCfg.CycleDays, startTime, endTime), nil
}
