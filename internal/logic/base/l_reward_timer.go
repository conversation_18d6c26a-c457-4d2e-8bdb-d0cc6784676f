package base

import (
	"activitysrv/config"
	"activitysrv/internal/dao/dao_activity"
	"context"
	"errors"
	"fmt"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	"git.keepfancy.xyz/back-end/frameworks/kit/dlm"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"
	"git.keepfancy.xyz/back-end/frameworks/lib/timex"
	"github.com/go-redis/redis/v8"
	"github.com/sirupsen/logrus"
)

// RunAutoRewardTask 执行自动领奖任务
func RunAutoRewardTask() {
	// 获取分布式锁，防止多实例重复执行
	isGet, unlock := dlm.DefaultLockMgr.OptimisticLockKey(config.RDS_LOCK_AUTO_REWARD_TIMER, 300)
	if !isGet {
		logrus.Debug("auto reward task is already running, skipping...")
		return
	}
	defer unlock()

	logrus.Info("starting auto reward task...")

	// todo 初始化上下文 后续读配置或者环境变量
	ctx := interceptor.NewRpcClientCtx(
		interceptor.WithProductId(int32(commonPB.PRODUCT_ID_PID_FISHER)),
		interceptor.WithChannelType(int32(commonPB.CHANNEL_TYPE_CT_GOOGLE)),
	)

	// 1. 获取所有活动列表
	allActivities := cmodel.GetAllActivity(consul_config.WithGrpcCtx(ctx))
	if allActivities == nil || len(allActivities) == 0 {
		logrus.Warnf("no activities found, auto reward task completed")
		return
	}

	// 2. 遍历每个活动
	for activityId, activityCfg := range allActivities {
		err := processActivityAutoReward(ctx, activityId, activityCfg)
		if err != nil {
			logrus.Errorf("RunAutoRewardTask failed to process auto reward for activity %d: %v", activityId, err)
		}
	}
}

// processActivityAutoReward 处理单个活动的自动领奖
func processActivityAutoReward(ctx context.Context, activityId int64, activityCfg *cmodel.Activity) error {
	logrus.Debugf("processing auto reward for activity: %d", activityId)

	// 1. 计算目标周期ID
	targetCycleId, err := calculateTargetCycleId(ctx, activityId, activityCfg)
	if err != nil {
		return err
	}
	if targetCycleId <= 0 {
		logrus.Errorf("no valid target cycle for activity %d auto reward", activityId)
		return nil
	}

	logrus.Infof("calculated auto reward target cycle for activity %d: cycleId=%d", activityId, targetCycleId)

	// 2. 获取并处理玩家列表
	players, err := getAndValidatePlayersInCycle(ctx, activityId, targetCycleId)
	if err != nil {
		return err
	}
	if len(players) == 0 {
		return nil
	}

	// 3. 为所有玩家执行自动领奖
	successCount := processAllPlayersAutoReward(ctx, players, activityId, targetCycleId, activityCfg)
	logrus.Infof("auto reward processed for activity %d cycle %d: total=%d, success=%d",
		activityId, targetCycleId, len(players), successCount)

	return nil
}

// calculateTargetCycleId 计算目标周期ID
func calculateTargetCycleId(ctx context.Context, activityId int64, activityCfg *cmodel.Activity) (int32, error) {
	if !activityCfg.IsLoop {
		return calculateNonLoopTargetCycle(activityId, activityCfg)
	}
	return calculateLoopTargetCycle(ctx, activityId, activityCfg)
}

// calculateNonLoopTargetCycle 计算非循环活动的目标周期
func calculateNonLoopTargetCycle(activityId int64, activityCfg *cmodel.Activity) (int32, error) {
	now := timex.Now().Unix()
	if activityCfg.CloseAt <= 0 {
		logrus.Debugf("activity %d has no close time, skipping auto reward", activityId)
		return 0, nil
	}

	// 检查是否在自动领奖时间窗口内（CloseAt后24小时内）
	autoRewardWindowEnd := activityCfg.CloseAt + 24*3600 // CloseAt后24小时
	if now <= activityCfg.CloseAt {
		logrus.Debugf("activity %d has not closed yet, skipping auto reward: now=%d, closeAt=%d",
			activityId, now, activityCfg.CloseAt)
		return 0, nil
	}
	if now > autoRewardWindowEnd {
		logrus.Debugf("activity %d auto reward window has expired, skipping: now=%d, windowEnd=%d",
			activityId, now, autoRewardWindowEnd)
		return 0, nil
	}
	return 1, nil
}

// calculateLoopTargetCycle 计算循环活动的目标周期
func calculateLoopTargetCycle(ctx context.Context, activityId int64, activityCfg *cmodel.Activity) (int32, error) {
	currentCycle, err := CheckAndCreateCycleIfNeeded(ctx, activityCfg)
	if err != nil {
		return 0, fmt.Errorf("failed to get current cycle for activity %d: %w", activityId, err)
	}
	return currentCycle.CycleId - 2, nil
}

// getAndValidatePlayersInCycle 获取并验证周期内的玩家列表
func getAndValidatePlayersInCycle(ctx context.Context, activityId int64, targetCycleId int32) ([]uint64, error) {
	players, err := getAllPlayersInCycle(ctx, activityId, targetCycleId)
	if err != nil {
		if errors.Is(err, redis.Nil) {
			logrus.Debugf("no player set found for activity %d cycle %d, skipping", activityId, targetCycleId)
			return nil, nil
		}
		return nil, err
	}

	// 如果Set为空，删除该Set
	if len(players) == 0 {
		err = dao_activity.DeletePlayerCycleSet(ctx, activityId, targetCycleId)
		if err != nil {
			logrus.Errorf("failed to delete empty player set for activity %d cycle %d: %v", activityId, targetCycleId, err)
		}
		logrus.Debugf("deleted empty player set for activity %d cycle %d", activityId, targetCycleId)
		return nil, nil
	}

	return players, nil
}

// processAllPlayersAutoReward 为所有玩家执行自动领奖
func processAllPlayersAutoReward(ctx context.Context, players []uint64, activityId int64, targetCycleId int32, activityCfg *cmodel.Activity) int {
	successCount := 0
	for _, playerId := range players {
		err := processPlayerAutoReward(ctx, playerId, activityId, targetCycleId, activityCfg)
		if err != nil {
			logrus.Errorf("failed to process auto reward for player %d in activity %d cycle %d: %v",
				playerId, activityId, targetCycleId, err)
		} else {
			successCount++
		}
	}
	return successCount
}

// getAllPlayersInCycle 获取周期内的所有玩家
func getAllPlayersInCycle(ctx context.Context, activityId int64, cycleId int32) ([]uint64, error) {
	// 先检查Set是否存在
	count, err := dao_activity.GetPlayerCycleCount(ctx, activityId, cycleId)
	if err != nil {
		return nil, err
	}
	if count == 0 {
		return []uint64{}, nil
	}

	// 分批获取所有玩家，避免一次性获取过多数据

	var allPlayers []uint64

	for {
		players, err := dao_activity.GetPlayersInCycle(ctx, activityId, cycleId, config.BatchSize)
		if err != nil {
			return nil, err
		}
		if len(players) == 0 {
			break
		}

		allPlayers = append(allPlayers, players...)

		// 如果返回的数量小于批次大小，说明已经获取完所有数据
		if len(players) < config.BatchSize {
			break
		}
	}

	return allPlayers, nil
}

// processPlayerAutoReward 为单个玩家处理自动领奖
func processPlayerAutoReward(ctx context.Context, playerId uint64, activityId int64, cycleId int32, activityCfg *cmodel.Activity) error {
	// 获取当前周期信息（自动领奖不需要校验周期有效性，因为是针对历史周期）
	currentCycle, err := dao_activity.GetCurrentCycle(ctx, activityId)
	if err != nil {
		return fmt.Errorf("failed to get current cycle: %w", err)
	}
	if currentCycle == nil {
		return fmt.Errorf("no active activity cycle currently")
	}

	// 调用通用领奖逻辑（自动领奖使用邮件发放）
	_, err = processRewardClaim(ctx, playerId, activityId, cycleId, activityCfg, currentCycle, RewardDeliveryMail)

	// 如果没有可领取的奖励，这不算错误
	if err != nil && err.Error() == "no reward stages available for claim currently" {
		logrus.Debugf("no rewards available for player %d in activity %d cycle %d", playerId, activityId, cycleId)
		// 即使没有奖励可领取，也要从Set中移除玩家
		_ = dao_activity.RemovePlayerFromCycle(ctx, activityId, cycleId, playerId)
		return nil
	}

	return err
}
