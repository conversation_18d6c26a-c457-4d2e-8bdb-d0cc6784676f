package proc

import (
	"activitysrv/internal/logic/base"
	"context"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"
	"sync"

	activityPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/activity"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
)

// ActivityService 活动服务实现
type ActivityService struct {
}

var (
	once     sync.Once
	instance *ActivityService
)

// GetActivityServiceInstance 获取活动服务单例
func GetActivityServiceInstance() *ActivityService {
	if instance != nil {
		return instance
	}

	once.Do(func() {
		instance = &ActivityService{}
	})
	return instance
}

// GetActivityProgress 获取活动进度
func (a *ActivityService) GetActivityProgress(ctx context.Context, req *activityPB.GetActivityProgressReq) *activityPB.GetActivityProgressRsp {
	entry := logx.NewLogEntry(ctx)
	rsp := &activityPB.GetActivityProgressRsp{
		Ret: protox.DefaultResult(),
	}

	playerId := interceptor.GetRPCOptions(ctx).PlayerId

	// 使用活动管理器获取进度
	progress, err := base.NewActivityLogic().GetProgress(ctx, playerId, req)
	if err != nil {
		entry.Errorf("failed to get activity progress: activityId=%d, playerId=%d, err=%v", req.ActivityId, playerId, err)
		rsp.Ret = protox.FillErrResult(err)
		return rsp
	}

	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)
	rsp.ActivityProgressList = progress
	entry.Debugf("successfully retrieved activity progress: activityId=%d, playerId=%d", req.ActivityId, playerId)
	return rsp
}

// ClaimActivityReward 领取活动奖励
func (a *ActivityService) ClaimActivityReward(ctx context.Context, req *activityPB.ClaimActivityRewardReq) *activityPB.ClaimActivityRewardRsp {
	entry := logx.NewLogEntry(ctx)
	playerId := interceptor.GetRPCOptions(ctx).PlayerId

	rsp, err := base.NewActivityLogic().ClaimReward(ctx, playerId, req)
	if err != nil {
		entry.Errorf("failed to claim activity reward: activityId=%d, playerId=%d, err=%v", req.ActivityId, playerId, err)
		rsp.Ret = protox.FillErrResult(err)
		return rsp
	}

	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)
	entry.Infof("successfully claimed activity reward: activityId=%d, playerId=%d", req.ActivityId, playerId)
	return rsp
}
